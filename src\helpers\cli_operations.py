"""
CLI Operations Helper - Handles command line operations for main.py
"""

import logging
import asyncio
from typing import List, Dict, Optional, Any
from datetime import datetime, timedelta

from src.database.models import MarketType
from src.services.bulk_data_service import BulkDataService
from src.services.fyers_symbol_service import FyersSymbolService
from src.services.csv_export_service import CSVExportService
from src.services.gap_filling_service import GapFillingService
from src.core.symbol_classifier import SymbolClassifier
from src.core.nse_symbol_processor import NSESymbolProcessor

logger = logging.getLogger(__name__)


class CLIOperations:
    """Helper class for command line operations."""
    
    def __init__(self):
        """Initialize CLI operations."""
        self.fyers_symbol_service = FyersSymbolService()
        self.symbol_classifier = SymbolClassifier()
        self.nse_processor = NSESymbolProcessor()
        self.csv_export_service = CSVExportService()
        self.gap_filling_service = GapFillingService()

        # Auto-resume functionality
        from src.services.auto_resume_service import AutoResumeService
        self.resume_service = AutoResumeService()
    
    def fetch_specific_symbol_data(self, symbol: str, market_type: str, days: int = 1) -> bool:
        """
        Fetch data for a specific symbol with exact Fyers symbol format.
        
        Args:
            symbol: Exact Fyers symbol (e.g., "NSE:RELIANCE-EQ")
            market_type: Market type string
            days: Number of days of data to fetch
            
        Returns:
            Success status
        """
        try:
            logger.info(f"🔄 Fetching {days} day(s) of data for {symbol} ({market_type})")
            
            # Convert market type string to enum
            market_type_enum = MarketType(market_type.upper())
            
            # Prepare symbols config for bulk service
            symbols_config = {market_type_enum: [symbol]}
            
            # Use bulk service to fetch data
            bulk_service = BulkDataService()
            results = asyncio.run(bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=days
            ))
            
            # Check results
            if market_type_enum in results:
                symbol_results = results[market_type_enum]
                success = symbol_results.get(symbol, False)
                
                if success:
                    logger.info(f"✅ Successfully fetched data for {symbol}")
                    return True
                else:
                    logger.error(f"❌ Failed to fetch data for {symbol}")
                    return False
            else:
                logger.error(f"❌ No results for market type {market_type}")
                return False
                
        except Exception as e:
            logger.error(f"Error fetching data for {symbol}: {e}")
            return False
    
    def fetch_multiple_symbols_data(self, symbols: List[str], market_type: str, days: int = 1) -> Dict[str, bool]:
        """
        Fetch data for multiple symbols of the same market type.
        
        Args:
            symbols: List of exact Fyers symbols
            market_type: Market type string
            days: Number of days of data to fetch
            
        Returns:
            Dict mapping symbol to success status
        """
        try:
            logger.info(f"🔄 Fetching {days} day(s) of data for {len(symbols)} {market_type} symbols")
            
            # Convert market type string to enum
            market_type_enum = MarketType(market_type.upper())
            
            # Prepare symbols config for bulk service
            symbols_config = {market_type_enum: symbols}
            
            # Use bulk service to fetch data
            bulk_service = BulkDataService()
            results = asyncio.run(bulk_service.populate_all_market_types(
                symbols_config=symbols_config,
                days=days
            ))
            
            # Extract results for the market type
            if market_type_enum in results:
                return results[market_type_enum]
            else:
                return {symbol: False for symbol in symbols}
                
        except Exception as e:
            logger.error(f"Error fetching data for multiple symbols: {e}")
            return {symbol: False for symbol in symbols}
    
    def fetch_all_symbols_from_mapping(self, market_type: str, days: int = 1, limit: Optional[int] = None) -> Dict[str, bool]:
        """
        Fetch data for all symbols of a market type from symbol_mapping table.
        
        Args:
            market_type: Market type string
            days: Number of days of data to fetch
            limit: Optional limit on number of symbols to process
            
        Returns:
            Dict mapping symbol to success status
        """
        try:
            logger.info(f"🔄 Fetching data for all {market_type} symbols from symbol_mapping table")
            
            # Get symbols from symbol_mapping table
            symbols = self._get_symbols_from_mapping(market_type, limit)
            
            if not symbols:
                logger.warning(f"No symbols found for market type {market_type}")
                return {}
            
            logger.info(f"Found {len(symbols)} symbols to process")
            
            # Fetch data for all symbols
            return self.fetch_multiple_symbols_data(symbols, market_type, days)
            
        except Exception as e:
            logger.error(f"Error fetching data for all symbols: {e}")
            return {}
    
    def _get_symbols_from_mapping(self, market_type: str, limit: Optional[int] = None,
                                 options_filters: Optional[Dict] = None,
                                 use_gap_filling: bool = True) -> List[str]:
        """Get Fyers symbols from symbol_mapping table with smart prioritization and gap filling."""
        try:
            # Special handling for OPTIONS market type
            if market_type.upper() == 'OPTIONS':
                return self._get_prioritized_options_symbols(limit, options_filters or {})

            # Use gap filling logic if enabled
            if use_gap_filling:
                return self._get_gap_prioritized_symbols(market_type, limit)
            else:
                return self._get_standard_prioritized_symbols(market_type, limit)

        except Exception as e:
            logger.error(f"Error getting symbols from mapping: {e}")
            return []

    def _get_prioritized_options_symbols(self, limit: Optional[int] = None,
                                       options_filters: Optional[Dict] = None) -> List[str]:
        """Get prioritized OPTIONS symbols using the OptionsPrioritizer service."""
        try:
            from src.services.options_prioritizer import OptionsPrioritizer

            logger.info("🎯 Using advanced OPTIONS symbol prioritization")

            # Extract filter parameters
            expiry_type = options_filters.get('expiry_type')
            expiry_months = options_filters.get('expiry_months')
            strike_range = options_filters.get('strike_range', 30)

            # Initialize prioritizer
            prioritizer = OptionsPrioritizer()

            # Get prioritized symbols
            prioritized_symbols = prioritizer.get_prioritized_options_symbols(
                expiry_type=expiry_type,
                expiry_months=expiry_months,
                strike_range=strike_range,
                limit=limit
            )

            # Get summary for logging
            summary = prioritizer.get_prioritization_summary(prioritized_symbols)

            logger.info("📊 OPTIONS Prioritization Summary:")
            logger.info(f"   Total symbols: {summary.get('total', 0)}")
            logger.info(f"   INDEX symbols: {summary.get('index_symbols', 0)}")
            logger.info(f"   Nifty 50 symbols: {summary.get('nifty50_symbols', 0)}")
            logger.info(f"   Other symbols: {summary.get('other_symbols', 0)}")
            logger.info(f"   Weekly options: {summary.get('weekly_options', 0)}")
            logger.info(f"   Monthly options: {summary.get('monthly_options', 0)}")

            return prioritized_symbols

        except Exception as e:
            logger.error(f"Error getting prioritized OPTIONS symbols: {e}")
            # Fallback to basic symbol retrieval
            logger.info("🔄 Falling back to basic OPTIONS symbol retrieval")
            return self._get_basic_options_symbols(limit)

    def _get_basic_options_symbols(self, limit: Optional[int] = None) -> List[str]:
        """Fallback method for basic OPTIONS symbol retrieval."""
        try:
            from src.database.connection import get_db
            from src.database.models import SymbolMapping, MarketType
            from sqlalchemy import and_

            db = next(get_db())

            try:
                # Get basic OPTIONS symbols with simple prioritization
                query = db.query(SymbolMapping).filter(
                    and_(
                        SymbolMapping.market_type == MarketType.OPTIONS,
                        SymbolMapping.is_active == True,
                        SymbolMapping.fyers_symbol.isnot(None)
                    )
                ).order_by(SymbolMapping.nse_symbol)

                if limit:
                    query = query.limit(limit)

                symbols = query.all()
                return [symbol.fyers_symbol for symbol in symbols if symbol.fyers_symbol]

            finally:
                db.close()

        except Exception as e:
            logger.error(f"Error in basic OPTIONS symbol retrieval: {e}")
            return []
    
    def fix_fyers_symbols_with_examples(self) -> bool:
        """Fix fyers_symbol columns using the exact examples provided by user."""
        try:
            logger.info("🔧 Fixing fyers_symbol columns with exact examples")
            
            # Exact symbols provided by user
            test_symbols = {
                'EQUITY': 'NSE:RELIANCE-EQ',
                'INDEX': 'NSE:NIFTY50-INDEX', 
                'FUTURES': 'NSE:RELIANCE25JULFUT',
                'OPTIONS': 'NSE:NIFTY25JUL25000CE'
            }
            
            # Fix specific symbols first
            fix_results = self.fyers_symbol_service.fix_specific_symbols(test_symbols)
            
            # Update all OHLCV tables
            update_results = self.fyers_symbol_service.update_fyers_symbols_in_ohlcv_tables()
            
            # Validate results
            validation_results = self.fyers_symbol_service.validate_fyers_symbols()
            
            # Log results
            logger.info("📊 Fix Results:")
            for market_type, success in fix_results.items():
                status = "✅" if success else "❌"
                logger.info(f"  {status} {market_type}: {test_symbols[market_type]}")
            
            logger.info("📊 Update Results:")
            for table, count in update_results.items():
                logger.info(f"  {table}: {count} rows updated")
            
            logger.info("📊 Validation Results:")
            for table, info in validation_results['ohlcv_tables'].items():
                null_count = info['null_fyers_symbol']
                total_count = info['total_records']
                logger.info(f"  {table}: {null_count}/{total_count} null fyers_symbol")
            
            return validation_results['validation_passed']
            
        except Exception as e:
            logger.error(f"Error fixing fyers_symbols: {e}")
            return False
    
    def process_symbols_with_resume(self, market_type: str, days: int = 1, 
                                   batch_size: int = 10, start_from: int = 0) -> Dict[str, Any]:
        """
        Process symbols with resume capability and error handling.
        
        Args:
            market_type: Market type to process
            days: Number of days of data to fetch
            batch_size: Number of symbols to process in each batch
            start_from: Index to start processing from (for resume)
            
        Returns:
            Processing results with resume information
        """
        try:
            logger.info(f"🔄 Processing {market_type} symbols with resume capability")
            
            # Get all symbols for the market type
            all_symbols = self._get_symbols_from_mapping(market_type)
            
            if not all_symbols:
                logger.warning(f"No symbols found for {market_type}")
                return {'success': False, 'error': 'No symbols found'}
            
            total_symbols = len(all_symbols)
            logger.info(f"Found {total_symbols} symbols to process")
            
            # Start from specified index
            symbols_to_process = all_symbols[start_from:]
            logger.info(f"Starting from index {start_from}, processing {len(symbols_to_process)} symbols")
            
            results = {
                'total_symbols': total_symbols,
                'processed_symbols': 0,
                'successful_symbols': 0,
                'failed_symbols': 0,
                'last_processed_index': start_from - 1,
                'symbol_results': {},
                'success': True
            }
            
            # Process in batches
            for i in range(0, len(symbols_to_process), batch_size):
                batch = symbols_to_process[i:i + batch_size]
                batch_start_index = start_from + i
                
                logger.info(f"Processing batch {i//batch_size + 1}: symbols {batch_start_index} to {batch_start_index + len(batch) - 1}")
                
                try:
                    # Process batch
                    batch_results = self.fetch_multiple_symbols_data(batch, market_type, days)
                    
                    # Update results
                    for symbol, success in batch_results.items():
                        results['symbol_results'][symbol] = success
                        results['processed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + batch.index(symbol)
                        
                        if success:
                            results['successful_symbols'] += 1
                        else:
                            results['failed_symbols'] += 1
                    
                    # Log batch progress
                    batch_success_count = sum(1 for success in batch_results.values() if success)
                    logger.info(f"Batch completed: {batch_success_count}/{len(batch)} successful")
                    
                except Exception as e:
                    logger.error(f"Error processing batch starting at {batch_start_index}: {e}")
                    # Mark all symbols in batch as failed
                    for symbol in batch:
                        results['symbol_results'][symbol] = False
                        results['processed_symbols'] += 1
                        results['failed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + batch.index(symbol)
            
            # Final summary
            success_rate = (results['successful_symbols'] / results['processed_symbols'] * 100) if results['processed_symbols'] > 0 else 0
            logger.info(f"📊 Processing Summary:")
            logger.info(f"  Total symbols: {results['total_symbols']}")
            logger.info(f"  Processed: {results['processed_symbols']}")
            logger.info(f"  Successful: {results['successful_symbols']}")
            logger.info(f"  Failed: {results['failed_symbols']}")
            logger.info(f"  Success rate: {success_rate:.1f}%")
            logger.info(f"  Last processed index: {results['last_processed_index']}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error in process_symbols_with_resume: {e}")
            return {'success': False, 'error': str(e)}

    def process_symbols_with_resume_and_dates(self, market_type: str, start_date: datetime, end_date: datetime,
                                            batch_size: int = 10, start_from: int = 0, limit: Optional[int] = None,
                                            options_filters: Optional[Dict] = None, auto_resume: bool = False) -> Dict[str, Any]:
        """
        HIGH-PERFORMANCE process symbols with auto-resume capability, parallel processing, and smart filtering.

        Args:
            market_type: Market type to process
            start_date: Start date for data fetching
            end_date: End date for data fetching
            batch_size: Number of symbols to process in each batch (increased for parallel processing)
            start_from: Index to start processing from (ignored if auto_resume=True)
            limit: Optional limit on number of symbols to process
            options_filters: Optional filters for OPTIONS symbols
            auto_resume: Enable automatic resume detection from database

        Returns:
            Processing results with resume information
        """
        import time
        from datetime import datetime as dt

        start_time = time.time()

        # AUTO-RESUME FUNCTIONALITY
        if auto_resume and market_type == 'OPTIONS':
            logger.info("🔍 AUTO-RESUME: Analyzing database for resume position...")
            resume_info = self.resume_service.find_resume_position(
                market_type, start_date, end_date, options_filters
            )

            # Log auto-resume summary
            summary = self.resume_service.create_processing_summary(resume_info)
            logger.info(summary)

            # Override start_from with auto-detected position
            start_from = resume_info.get('resume_from', 0)

            if start_from > 0:
                logger.info(f"🎯 AUTO-RESUME: Starting from position {start_from}")
            else:
                logger.info("🆕 AUTO-RESUME: Starting from beginning")

        # CSV EXPORT FOR SYMBOL PLANNING
        if market_type == 'OPTIONS':
            self._export_symbols_to_csv(options_filters, start_date, end_date)

        try:
            logger.info(f"🔄 Processing {market_type} symbols with optimized performance")
            logger.info(f"📅 Date range: {start_date.date()} to {end_date.date()}")

            # Get all symbols for the market type (with OPTIONS filters if applicable)
            all_symbols = self._get_symbols_from_mapping(market_type, limit, options_filters)

            if not all_symbols:
                logger.warning(f"No symbols found for {market_type}")
                return {'success': False, 'error': 'No symbols found'}

            total_symbols = len(all_symbols)
            logger.info(f"Found {total_symbols} total symbols, processing from index {start_from}")

            # Start from specified index
            symbols_to_process = all_symbols[start_from:]

            results = {
                'total_symbols': total_symbols,
                'processed_symbols': 0,
                'successful_symbols': 0,
                'failed_symbols': 0,
                'last_processed_index': start_from - 1,
                'symbol_results': {},
                'success': True,
                'start_date': start_date.isoformat(),
                'end_date': end_date.isoformat(),
                'processing_time': 0,
                'error_symbols': []
            }

            # Process in batches with optimized logging
            total_batches = (len(symbols_to_process) + batch_size - 1) // batch_size

            for i in range(0, len(symbols_to_process), batch_size):
                batch_start_time = time.time()
                batch = symbols_to_process[i:i + batch_size]
                batch_start_index = start_from + i
                batch_num = i // batch_size + 1

                # Enhanced progress tracking - show every 5 batches or for small batches
                if batch_num % 5 == 1 or total_batches <= 10 or batch_num == total_batches:
                    progress_pct = (batch_num / total_batches) * 100
                    logger.info(f"📊 {market_type} Progress: Batch {batch_num}/{total_batches} ({progress_pct:.1f}%) - Processing symbols {batch_start_index}-{batch_start_index + len(batch) - 1}")

                try:
                    # Process batch with date range
                    batch_results = self.fetch_multiple_symbols_data_with_dates(batch, market_type, start_date, end_date)

                    # Update results
                    for j, symbol in enumerate(batch):
                        success = batch_results.get(symbol, False)
                        results['symbol_results'][symbol] = success
                        results['processed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + j

                        if success:
                            results['successful_symbols'] += 1
                        else:
                            results['failed_symbols'] += 1
                            results['error_symbols'].append(symbol)

                    # Calculate batch time and show progress
                    batch_time = time.time() - batch_start_time
                    batch_success_rate = (sum(1 for s in batch_results.values() if s) / len(batch_results)) * 100

                    # Show progress every 5 batches or for important milestones
                    if batch_num % 5 == 0 or batch_num == total_batches or total_batches <= 10:
                        progress_pct = (batch_num / total_batches) * 100
                        logger.info(f"✅ Progress: {progress_pct:.1f}% | Batch {batch_num}: {batch_success_rate:.1f}% success | Time: {batch_time:.1f}s")

                except Exception as e:
                    logger.error(f"❌ Error in batch {batch_num}: {e}")
                    # Mark all symbols in this batch as failed
                    for j, symbol in enumerate(batch):
                        results['symbol_results'][symbol] = False
                        results['processed_symbols'] += 1
                        results['failed_symbols'] += 1
                        results['last_processed_index'] = batch_start_index + j
                        results['error_symbols'].append(symbol)

            # Calculate total processing time
            total_time = time.time() - start_time
            results['processing_time'] = total_time

            # Generate error file if there are failed symbols
            if results['error_symbols']:
                self._write_error_symbols_file(results['error_symbols'], market_type, start_date, end_date)

            # Generate CSV export for this market type
            self._export_symbols_csv(market_type, results['symbol_results'])

            # Final summary with performance metrics
            success_rate = (results['successful_symbols'] / results['processed_symbols']) * 100 if results['processed_symbols'] > 0 else 0
            avg_time_per_symbol = total_time / results['processed_symbols'] if results['processed_symbols'] > 0 else 0

            logger.info(f"\n🎯 FINAL SUMMARY - {market_type} Processing:")
            logger.info(f"  ✅ Successful: {results['successful_symbols']}/{results['processed_symbols']} ({success_rate:.1f}%)")
            logger.info(f"  ❌ Failed: {results['failed_symbols']} symbols")
            logger.info(f"  ⏱️  Total time: {total_time:.1f}s ({avg_time_per_symbol:.2f}s per symbol)")
            logger.info(f"  📅 Date range: {start_date.date()} to {end_date.date()}")

            if results['error_symbols']:
                logger.info(f"  📄 Error symbols saved to: logs/error_symbols_{market_type}_{dt.now().strftime('%Y%m%d_%H%M%S')}.txt")

            return results

        except Exception as e:
            logger.error(f"Error in process_symbols_with_resume_and_dates: {e}")
            return {'success': False, 'error': str(e)}

    def _export_symbols_to_csv(self, options_filters: Optional[Dict], start_date: datetime, end_date: datetime) -> None:
        """Export planned symbols to CSV file for transparency and tracking."""
        try:
            import csv
            import os
            from datetime import datetime as dt

            logger.info("📄 Exporting planned symbols to CSV...")

            # Get all symbols that will be processed
            if options_filters:
                from src.services.options_prioritizer import OptionsPrioritizer
                prioritizer = OptionsPrioritizer()

                expiry_type = options_filters.get('expiry_type')
                expiry_months = options_filters.get('expiry_months')
                strike_range = options_filters.get('strike_range', 30)
                limit = options_filters.get('limit')

                fyers_symbols = prioritizer.get_prioritized_options_symbols(
                    expiry_type=expiry_type,
                    expiry_months=expiry_months,
                    strike_range=strike_range,
                    limit=limit
                )
            else:
                fyers_symbols = []

            if not fyers_symbols:
                logger.warning("No symbols to export to CSV")
                return

            # Create exports directory if it doesn't exist
            exports_dir = "exports"
            os.makedirs(exports_dir, exist_ok=True)

            # Generate CSV filename with timestamp
            timestamp = dt.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"options_symbols_plan_{timestamp}.csv"
            csv_path = os.path.join(exports_dir, csv_filename)

            # Write symbols to CSV
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write header
                writer.writerow([
                    'Index', 'Fyers_Symbol', 'Underlying', 'Expiry_Date',
                    'Strike_Price', 'Option_Type', 'Processing_Date_Range'
                ])

                # Write symbol data
                for i, symbol in enumerate(fyers_symbols, 1):
                    # Extract symbol components for better tracking
                    underlying = self._extract_underlying_from_symbol(symbol)
                    expiry_date = self._extract_expiry_from_symbol(symbol)
                    strike_price = self._extract_strike_from_symbol(symbol)
                    option_type = self._extract_option_type_from_symbol(symbol)
                    date_range = f"{start_date.date()} to {end_date.date()}"

                    writer.writerow([
                        i, symbol, underlying, expiry_date,
                        strike_price, option_type, date_range
                    ])

            logger.info(f"📄 Exported {len(fyers_symbols)} symbols to: {csv_path}")
            logger.info(f"📊 CSV contains planned processing order and symbol details")

        except Exception as e:
            logger.error(f"Error exporting symbols to CSV: {e}")

    def _extract_underlying_from_symbol(self, symbol: str) -> str:
        """Extract underlying symbol from Fyers symbol."""
        try:
            # Remove NSE: prefix and extract underlying
            clean_symbol = symbol.replace('NSE:', '')
            # Pattern: UNDERLYING25JULSTRIKECE/PE
            import re
            match = re.match(r'^([A-Z]+)', clean_symbol)
            return match.group(1) if match else 'Unknown'
        except:
            return 'Unknown'

    def _extract_expiry_from_symbol(self, symbol: str) -> str:
        """Extract expiry date from Fyers symbol."""
        try:
            import re
            # Pattern: 25JUL or similar
            match = re.search(r'(\d{2}[A-Z]{3})', symbol)
            return match.group(1) if match else 'Unknown'
        except:
            return 'Unknown'

    def _extract_strike_from_symbol(self, symbol: str) -> str:
        """Extract strike price from Fyers symbol."""
        try:
            import re
            # Pattern: numbers before CE/PE
            match = re.search(r'(\d+)(?:CE|PE)$', symbol)
            return match.group(1) if match else 'Unknown'
        except:
            return 'Unknown'

    def _extract_option_type_from_symbol(self, symbol: str) -> str:
        """Extract option type (CE/PE) from Fyers symbol."""
        try:
            if symbol.endswith('CE'):
                return 'CE'
            elif symbol.endswith('PE'):
                return 'PE'
            else:
                return 'Unknown'
        except:
            return 'Unknown'

    def _write_error_symbols_file(self, error_symbols: List[str], market_type: str, start_date: datetime, end_date: datetime) -> None:
        """Write error symbols to a text file for later analysis."""
        try:
            from datetime import datetime as dt
            import os

            # Ensure logs directory exists
            os.makedirs('logs', exist_ok=True)

            # Create filename with timestamp
            timestamp = dt.now().strftime('%Y%m%d_%H%M%S')
            filename = f"logs/error_symbols_{market_type}_{timestamp}.txt"

            with open(filename, 'w') as f:
                f.write(f"Error Symbols Report - {market_type}\n")
                f.write(f"Generated: {dt.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Date Range: {start_date.date()} to {end_date.date()}\n")
                f.write(f"Total Failed Symbols: {len(error_symbols)}\n")
                f.write("=" * 50 + "\n\n")

                for i, symbol in enumerate(error_symbols, 1):
                    f.write(f"{i:4d}. {symbol}\n")

                f.write(f"\n" + "=" * 50 + "\n")
                f.write(f"End of Report\n")

            logger.info(f"📄 Error symbols written to: {filename}")

        except Exception as e:
            logger.warning(f"Failed to write error symbols file: {e}")

    def _export_symbols_csv(self, market_type: str, symbol_results: Dict[str, bool]) -> None:
        """Export symbols processing results to CSV file."""
        try:
            # Convert market_type string to MarketType enum
            market_type_enum = MarketType(market_type)

            # Export CSV for this market type
            csv_path = self.csv_export_service.export_symbols_for_market_type(
                market_type=market_type_enum,
                processing_results=symbol_results
            )

            if csv_path:
                logger.info(f"📊 CSV export completed: {csv_path}")

        except Exception as e:
            logger.warning(f"Failed to export CSV for {market_type}: {e}")

    def _get_gap_prioritized_symbols(self, market_type: str, limit: Optional[int] = None) -> List[str]:
        """Get symbols prioritized by data gaps for gap filling."""
        try:
            logger.info(f"🎯 Using gap-filling prioritization for {market_type}")

            # Convert market_type string to enum
            from src.database.models import MarketType
            market_type_enum = MarketType(market_type.upper())

            # Get gap-prioritized symbols
            gap_symbols = self.gap_filling_service.get_prioritized_symbols_for_gap_filling(
                market_types=[market_type_enum],
                max_symbols_per_type=limit or 100,
                max_gap_days=30
            )

            # Extract symbols for this market type
            symbols = gap_symbols.get(market_type_enum, [])

            if symbols:
                logger.info(f"📋 Found {len(symbols)} gap-prioritized symbols for {market_type}")
                # Log top 5 symbols for visibility
                top_symbols = symbols[:5]
                logger.info(f"   Top priorities: {', '.join(top_symbols)}")
            else:
                logger.info(f"✅ No data gaps found for {market_type}, using standard prioritization")
                # Fall back to standard prioritization
                return self._get_standard_prioritized_symbols(market_type, limit)

            return symbols

        except Exception as e:
            logger.warning(f"Error in gap prioritization for {market_type}: {e}")
            # Fall back to standard prioritization
            return self._get_standard_prioritized_symbols(market_type, limit)

    def _get_standard_prioritized_symbols(self, market_type: str, limit: Optional[int] = None) -> List[str]:
        """Get symbols using standard prioritization (original logic)."""
        try:
            from src.database.connection import get_db
            from src.database.models import SymbolMapping, MarketType
            from sqlalchemy import and_, text

            db = next(get_db())
            market_type_enum = MarketType(market_type.upper())

            # Define prioritization patterns for each market type
            priority_patterns = {
                'INDEX': {
                    'high_priority': ['NIFTY50', 'NIFTY100', 'FINNIFTY', 'MIDCPNIFTY', 'NIFTYIT', 'NIFTYPHARMA', 'NIFTYAUTO', 'NIFTYMETAL', 'NIFTYFMCG'],
                    'exclude_patterns': ['BHARATBOND', 'DJIA', 'FTSE', 'S&P', 'HANGSENG']
                },
                'EQUITY': {
                    'high_priority': ['RELIANCE', 'TCS', 'INFY', 'HDFCBANK', 'ICICIBANK', 'SBIN', 'BHARTIARTL', 'ITC', 'KOTAKBANK'],
                    'exclude_patterns': []
                },
                'FUTURES': {
                    'high_priority': ['RELIANCE', 'NIFTY', 'BANKNIFTY', 'TCS', 'INFY'],
                    'exclude_patterns': []
                }
            }

            patterns = priority_patterns.get(market_type.upper(), {'high_priority': [], 'exclude_patterns': []})
            high_priority = patterns['high_priority']
            exclude_patterns = patterns['exclude_patterns']

            # Get all symbols first
            all_mappings = db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.market_type == market_type_enum,
                    SymbolMapping.is_active == True,
                    SymbolMapping.fyers_symbol.isnot(None)
                )
            ).all()

            # Categorize symbols
            high_priority_symbols = []
            medium_priority_symbols = []
            low_priority_symbols = []

            for mapping in all_mappings:
                nse_symbol = mapping.nse_symbol.upper()

                # Check for exclusion patterns (low priority)
                should_exclude = any(pattern in nse_symbol for pattern in exclude_patterns)
                if should_exclude:
                    low_priority_symbols.append(mapping.fyers_symbol)
                    continue

                # Check for high priority patterns
                is_high_priority = any(pattern in nse_symbol for pattern in high_priority)
                if is_high_priority:
                    high_priority_symbols.append(mapping.fyers_symbol)
                else:
                    medium_priority_symbols.append(mapping.fyers_symbol)

            # Combine in priority order
            prioritized_symbols = high_priority_symbols + medium_priority_symbols + low_priority_symbols

            # Apply limit if specified
            if limit:
                prioritized_symbols = prioritized_symbols[:limit]

            return prioritized_symbols

        except Exception as e:
            logger.error(f"Error in standard prioritization for {market_type}: {e}")
            return []

    def fetch_multiple_symbols_data_with_dates(self, symbols: List[str], market_type: str,
                                             start_date: datetime, end_date: datetime) -> Dict[str, bool]:
        """
        Fetch data for multiple symbols with specific date range.

        Args:
            symbols: List of symbols to fetch data for
            market_type: Market type string
            start_date: Start date for data fetching
            end_date: End date for data fetching

        Returns:
            Dict mapping symbol to success status
        """
        try:
            logger.info(f"🔄 Fetching data for {len(symbols)} {market_type} symbols from {start_date.date()} to {end_date.date()}")

            # Convert market type string to enum
            market_type_enum = MarketType(market_type.upper())

            # Prepare symbols config for bulk service
            symbols_config = {market_type_enum: symbols}

            # Use HIGH-PERFORMANCE parallel bulk service for data fetching
            bulk_service = BulkDataService()

            # Use parallel processing for OPTIONS (significant performance improvement)
            if market_type_enum == MarketType.OPTIONS:
                logger.info("🚀 Using HIGH-PERFORMANCE parallel processing for OPTIONS")
                results = {market_type_enum: asyncio.run(bulk_service.populate_market_type_parallel(
                    symbols=symbols,
                    market_type=market_type_enum,
                    start_date=start_date,
                    end_date=end_date
                ))}
            else:
                # Use standard processing for other market types
                results = asyncio.run(bulk_service.populate_all_market_types_with_dates(
                    symbols_config=symbols_config,
                    start_date=start_date,
                    end_date=end_date
                ))

            # Extract results for the market type
            if market_type_enum in results:
                return results[market_type_enum]
            else:
                return {symbol: False for symbol in symbols}

        except Exception as e:
            logger.error(f"Error fetching data for symbols: {e}")
            return {symbol: False for symbol in symbols}

    def check_and_resume_incomplete_data(self, symbol: str, market_type: str,
                                       start_date: datetime, end_date: datetime) -> Optional[datetime]:
        """
        Check for incomplete data and return the last incomplete datetime for resume.

        Args:
            symbol: Symbol to check
            market_type: Market type
            start_date: Original start date
            end_date: Original end date

        Returns:
            Last incomplete datetime if gaps found, None if complete
        """
        try:
            from ..database.connection import get_db
            from sqlalchemy import text

            # Get the appropriate table name
            table_map = {
                'EQUITY': 'equity_ohlcv',
                'INDEX': 'index_ohlcv',
                'FUTURES': 'futures_ohlcv',
                'OPTIONS': 'options_ohlcv'
            }

            table_name = table_map.get(market_type.upper())
            if not table_name:
                logger.warning(f"Unknown market type: {market_type}")
                return None

            with get_db() as db:
                # Check for the latest data for this symbol
                query = text(f"""
                    SELECT MAX(datetime) as last_datetime
                    FROM {table_name}
                    WHERE symbol = :symbol
                    AND datetime >= :start_date
                    AND datetime <= :end_date
                """)

                result = db.execute(query, {
                    'symbol': symbol,
                    'start_date': start_date,
                    'end_date': end_date
                }).fetchone()

                if result and result.last_datetime:
                    last_datetime = result.last_datetime
                    if isinstance(last_datetime, str):
                        last_datetime = datetime.fromisoformat(last_datetime.replace('Z', '+00:00'))

                    # If there's a gap between last data and end date, resume from last datetime
                    if last_datetime < end_date:
                        gap_hours = (end_date - last_datetime).total_seconds() / 3600
                        if gap_hours > 24:  # More than 1 day gap
                            logger.info(f"📅 Found data gap for {symbol}: last data at {last_datetime}, resuming from there")
                            return last_datetime
                        else:
                            logger.info(f"✅ {symbol} data is up to date (gap: {gap_hours:.1f} hours)")
                            return None
                else:
                    logger.info(f"📅 No existing data found for {symbol}, will fetch full range")
                    return start_date

        except Exception as e:
            logger.warning(f"Error checking incomplete data for {symbol}: {e}")
            return None

    def get_symbols_with_resume_info(self, market_type: str, start_date: datetime,
                                   end_date: datetime, limit: Optional[int] = None, options_filters: Optional[Dict] = None) -> List[Dict[str, Any]]:
        """
        Get symbols with resume information for incomplete data.

        Args:
            market_type: Market type to process
            start_date: Start date for checking
            end_date: End date for checking
            limit: Optional limit on number of symbols

        Returns:
            List of symbol dictionaries with resume information
        """
        try:
            # Get all symbols for the market type (with OPTIONS filters if applicable)
            all_symbols = self._get_symbols_from_mapping(market_type, limit, options_filters)

            if not all_symbols:
                logger.warning(f"No symbols found for {market_type}")
                return []

            symbols_with_resume = []

            for symbol in all_symbols:
                resume_from = self.check_and_resume_incomplete_data(symbol, market_type, start_date, end_date)

                symbol_info = {
                    'symbol': symbol,
                    'needs_data': resume_from is not None,
                    'resume_from': resume_from,
                    'original_start': start_date,
                    'original_end': end_date
                }

                symbols_with_resume.append(symbol_info)

            # Filter to only symbols that need data
            symbols_needing_data = [s for s in symbols_with_resume if s['needs_data']]

            logger.info(f"📊 Resume Analysis for {market_type}:")
            logger.info(f"  Total symbols: {len(all_symbols)}")
            logger.info(f"  Symbols needing data: {len(symbols_needing_data)}")
            logger.info(f"  Symbols up to date: {len(all_symbols) - len(symbols_needing_data)}")

            return symbols_needing_data

        except Exception as e:
            logger.error(f"Error getting symbols with resume info: {e}")
            return []
