"""
Auto-Resume Service for OPTIONS data processing.
Automatically detects where processing was last interrupted and resumes from there.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List
from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from src.database.connection import get_db
from src.database.models import SymbolMapping, MarketType, OptionsOHLCV

logger = logging.getLogger(__name__)


class AutoResumeService:
    """Service for automatic resume functionality in OPTIONS data processing."""
    
    def __init__(self):
        """Initialize the auto-resume service."""
        self.db: Session = next(get_db())
    
    def find_resume_position(self, market_type: str, start_date: datetime, end_date: datetime,
                           options_filters: Optional[Dict] = None) -> Dict[str, Any]:
        """
        Automatically find the resume position for OPTIONS processing.
        
        Args:
            market_type: Market type (should be 'OPTIONS')
            start_date: Start date for data processing
            end_date: End date for data processing
            options_filters: Optional filters for OPTIONS symbols
            
        Returns:
            Dictionary with resume information
        """
        try:
            logger.info("🔍 AUTO-RESUME: Analyzing database to find last processed position...")
            
            if market_type != 'OPTIONS':
                logger.info("Auto-resume currently only supports OPTIONS market type")
                return {'resume_from': 0, 'reason': 'Not OPTIONS market type'}
            
            # Get all symbols that should be processed (same logic as main processing)
            all_symbols = self._get_all_symbols_for_processing(options_filters)
            
            if not all_symbols:
                logger.warning("No symbols found for processing")
                return {'resume_from': 0, 'reason': 'No symbols found'}
            
            logger.info(f"📊 Total symbols to process: {len(all_symbols)}")
            
            # Find symbols that already have data for the requested date range
            symbols_with_data = self._find_symbols_with_data(all_symbols, start_date, end_date)
            
            logger.info(f"📊 Symbols with existing data: {len(symbols_with_data)}")
            
            # Find the resume position
            resume_position = self._calculate_resume_position(all_symbols, symbols_with_data)
            
            # Log resume information
            if resume_position > 0:
                logger.info(f"🎯 AUTO-RESUME: Found last processed position at index {resume_position}")
                logger.info(f"📊 Skipping {resume_position} symbols that already have data")
                logger.info(f"🔄 Will process {len(all_symbols) - resume_position} remaining symbols")
            else:
                logger.info("🆕 AUTO-RESUME: Starting from beginning (no existing data found)")
            
            return {
                'resume_from': resume_position,
                'total_symbols': len(all_symbols),
                'symbols_with_data': len(symbols_with_data),
                'remaining_symbols': len(all_symbols) - resume_position,
                'reason': f'Found {len(symbols_with_data)} symbols with existing data'
            }
            
        except Exception as e:
            logger.error(f"Error in auto-resume analysis: {e}")
            return {'resume_from': 0, 'reason': f'Error: {e}'}
    
    def _get_all_symbols_for_processing(self, options_filters: Optional[Dict] = None) -> List[str]:
        """Get all symbols that should be processed (same order as main processing)."""
        try:
            from src.services.options_prioritizer import OptionsPrioritizer
            
            # Use the same prioritizer logic as main processing
            prioritizer = OptionsPrioritizer()
            
            # Extract filters
            expiry_type = options_filters.get('expiry_type') if options_filters else None
            expiry_months = options_filters.get('expiry_months') if options_filters else None
            strike_range = options_filters.get('strike_range', 30) if options_filters else 30
            limit = options_filters.get('limit') if options_filters else None
            
            # Get prioritized symbols (same as main processing)
            fyers_symbols = prioritizer.get_prioritized_options_symbols(
                expiry_type=expiry_type,
                expiry_months=expiry_months,
                strike_range=strike_range,
                limit=limit
            )
            
            return fyers_symbols
            
        except Exception as e:
            logger.error(f"Error getting symbols for processing: {e}")
            return []
    
    def _find_symbols_with_data(self, all_symbols: List[str], start_date: datetime, end_date: datetime) -> set:
        """Find symbols that already have data for the specified date range."""
        try:
            # Query for symbols that have data in the specified date range
            query = self.db.query(OptionsOHLCV.fyers_symbol).filter(
                OptionsOHLCV.fyers_symbol.in_(all_symbols),
                OptionsOHLCV.datetime >= start_date,
                OptionsOHLCV.datetime <= end_date
            ).distinct()
            
            symbols_with_data = {row.fyers_symbol for row in query.all()}
            
            return symbols_with_data
            
        except Exception as e:
            logger.error(f"Error finding symbols with data: {e}")
            return set()
    
    def _calculate_resume_position(self, all_symbols: List[str], symbols_with_data: set) -> int:
        """Calculate the resume position based on symbols with existing data."""
        try:
            # Find the last consecutive symbol with data
            resume_position = 0
            
            for i, symbol in enumerate(all_symbols):
                if symbol in symbols_with_data:
                    resume_position = i + 1  # Resume after this symbol
                else:
                    # Found first symbol without data, resume from here
                    break
            
            return resume_position
            
        except Exception as e:
            logger.error(f"Error calculating resume position: {e}")
            return 0
    
    def create_processing_summary(self, resume_info: Dict[str, Any]) -> str:
        """Create a summary of the auto-resume analysis."""
        try:
            summary = []
            summary.append("🔍 AUTO-RESUME ANALYSIS SUMMARY")
            summary.append("=" * 50)
            summary.append(f"📊 Total symbols to process: {resume_info.get('total_symbols', 0)}")
            summary.append(f"✅ Symbols with existing data: {resume_info.get('symbols_with_data', 0)}")
            summary.append(f"🔄 Remaining symbols to process: {resume_info.get('remaining_symbols', 0)}")
            summary.append(f"🎯 Resume position: {resume_info.get('resume_from', 0)}")
            summary.append(f"💡 Reason: {resume_info.get('reason', 'Unknown')}")
            summary.append("=" * 50)
            
            return "\n".join(summary)
            
        except Exception as e:
            logger.error(f"Error creating processing summary: {e}")
            return "Error creating summary"
