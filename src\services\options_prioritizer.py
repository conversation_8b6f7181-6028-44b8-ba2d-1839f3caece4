"""
OPTIONS Symbol Prioritization Service.
Implements intelligent symbol selection for OPTIONS market type with prioritization logic.
"""

import logging
import re
from typing import Dict, List, Optional, Set, Tuple
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from src.database.connection import get_db
from src.database.models import SymbolMapping, MarketType, OptionType
from src.services.spot_price_service import SpotPriceService

logger = logging.getLogger(__name__)

# Nifty 50 symbols list (as of 2024)
NIFTY_50_SYMBOLS = [
    'ADANIENT', 'ADANIPORTS', 'APOLLOHOSP', 'ASIANPAINT', 'AXISBANK', 'BAJAJ-AUTO',
    'BAJAJFINSV', 'BAJFINANCE', 'BHARTIARTL', 'BPCL', 'BRITANNIA', 'CIPLA', 'COALINDIA',
    'DIVISLAB', 'DRREDDY', 'EICHERMOT', 'GRASIM', 'HCLTECH', 'HDFCBANK', 'HD<PERSON><PERSON>IFE',
    'HEROMOTOCO', 'HINDALCO', 'HINDUNIL<PERSON>', 'ICICIBANK', 'INDUSINDBK', 'INFY', 'ITC',
    'JSWSTEEL', 'KOTAKBANK', 'LT', 'M&M', 'MARUTI', 'NESTLEIND', 'NTPC', 'ONGC',
    'POWERGRID', 'RELIANCE', 'SBILIFE', 'SBIN', 'SUNPHARMA', 'TATACONSUM', 'TATAMOTORS',
    'TATASTEEL', 'TCS', 'TECHM', 'TITAN', 'ULTRACEMCO', 'UPL', 'WIPRO'
]

# Major INDEX symbols for OPTIONS
MAJOR_INDEX_SYMBOLS = [
    'NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY', 'NIFTYIT', 'NIFTYPHARMA',
    'NIFTYAUTO', 'NIFTYMETAL', 'NIFTYFMCG', 'NIFTYBANK'
]

# Month abbreviations mapping
MONTH_MAPPING = {
    'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
    'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
}


class OptionsPrioritizer:
    """Service for prioritizing OPTIONS symbols based on various criteria."""
    
    def __init__(self, db_session: Optional[Session] = None):
        """Initialize the prioritizer."""
        self.db = db_session or next(get_db())
        self.spot_price_service = SpotPriceService(self.db)
        self.current_date = datetime.now()
        self.current_month = self.current_date.month

        # Performance optimization caches
        self._underlying_cache = {}  # Cache for extracted underlying symbols
        self._spot_price_cache = {}  # Cache for spot prices
        self._strike_relevance_cache = {}  # Cache for strike relevance calculations
        
    def get_prioritized_options_symbols(self, 
                                      expiry_type: Optional[str] = None,
                                      expiry_months: Optional[List[str]] = None,
                                      strike_range: int = 30,
                                      limit: Optional[int] = None) -> List[str]:
        """
        Get prioritized list of OPTIONS symbols based on criteria.
        
        Args:
            expiry_type: 'WEEKLY' or 'MONTHLY' filter
            expiry_months: List of month abbreviations (e.g., ['JUL', 'AUG'])
            strike_range: Strike price range from spot price
            limit: Maximum number of symbols to return
            
        Returns:
            List of prioritized Fyers symbols
        """
        try:
            logger.info("🎯 Starting OPTIONS symbol prioritization")
            logger.info(f"   Expiry type: {expiry_type or 'ALL'}")
            logger.info(f"   Expiry months: {expiry_months or 'ALL'}")
            logger.info(f"   Strike range: ±{strike_range}")
            logger.info(f"   Limit: {limit or 'UNLIMITED'}")
            
            # Get all OPTIONS symbols from database
            all_options = self._get_all_options_symbols()
            logger.info(f"📊 Found {len(all_options)} total OPTIONS symbols")
            
            if not all_options:
                logger.warning("❌ No OPTIONS symbols found in database")
                return []
            
            # Apply filters
            filtered_options = self._apply_filters(all_options, expiry_type, expiry_months)
            logger.info(f"🔍 After filtering: {len(filtered_options)} symbols")
            
            # Prioritize symbols
            prioritized_options = self._prioritize_symbols(filtered_options, strike_range)
            logger.info(f"📈 After prioritization: {len(prioritized_options)} symbols")
            
            # Apply limit
            if limit:
                prioritized_options = prioritized_options[:limit]
                logger.info(f"✂️ After limit: {len(prioritized_options)} symbols")
            
            # Extract Fyers symbols
            fyers_symbols = [option.fyers_symbol for option in prioritized_options if option.fyers_symbol]
            
            logger.info(f"✅ Final prioritized OPTIONS symbols: {len(fyers_symbols)}")
            return fyers_symbols
            
        except Exception as e:
            logger.error(f"❌ Error in OPTIONS symbol prioritization: {e}")
            return []
    
    def _get_all_options_symbols(self) -> List[SymbolMapping]:
        """Get all OPTIONS symbols from database with optimized query."""
        try:
            # Optimized query with selective loading and indexing
            query = self.db.query(SymbolMapping).filter(
                and_(
                    SymbolMapping.market_type == MarketType.OPTIONS,
                    SymbolMapping.is_active == True,
                    SymbolMapping.fyers_symbol.isnot(None),
                    SymbolMapping.expiry_date.isnot(None),
                    SymbolMapping.strike_price.isnot(None),
                    SymbolMapping.option_type.isnot(None)
                )
            ).order_by(
                SymbolMapping.expiry_date,  # Order by expiry for better cache locality
                SymbolMapping.nse_symbol
            )

            # Use batch loading for better performance
            return query.all()

        except Exception as e:
            logger.error(f"Error getting OPTIONS symbols from database: {e}")
            return []
    
    def _apply_filters(self, options: List[SymbolMapping],
                      expiry_type: Optional[str],
                      expiry_months: Optional[List[str]]) -> List[SymbolMapping]:
        """Apply enhanced smart filters to OPTIONS symbols for performance optimization."""
        filtered = options

        # 1. SMART EXPIRY DATE FILTERING - Remove expired and far-future options
        today = self.current_date.date()
        max_expiry_date = today + timedelta(days=90)  # Only next 3 months for liquidity

        filtered = [
            opt for opt in filtered
            if opt.expiry_date and today <= opt.expiry_date <= max_expiry_date
        ]
        logger.info(f"   After smart expiry filter (next 90 days): {len(filtered)} symbols")

        # 2. Filter by expiry type (WEEKLY/MONTHLY)
        if expiry_type:
            filtered = [opt for opt in filtered if self._matches_expiry_type(opt, expiry_type)]
            logger.info(f"   After expiry type filter ({expiry_type}): {len(filtered)} symbols")

        # 3. Filter by expiry months
        if expiry_months:
            filtered = [opt for opt in filtered if self._matches_expiry_months(opt, expiry_months)]
            logger.info(f"   After month filter ({expiry_months}): {len(filtered)} symbols")

        # 4. PRE-VALIDATION CHECK - Remove symbols that already have recent data
        filtered = self._filter_symbols_with_existing_data(filtered)
        logger.info(f"   After existing data filter: {len(filtered)} symbols")

        return filtered
    
    def _matches_expiry_type(self, option: SymbolMapping, expiry_type: str) -> bool:
        """Check if option matches the expiry type (WEEKLY/MONTHLY)."""
        symbol = option.nse_symbol.replace('NSE:', '')
        
        if expiry_type == 'MONTHLY':
            # Monthly pattern: UNDERLYING25JULSTRIKECE/PE
            return bool(re.search(r'\d{2}[A-Z]{3}', symbol))
        elif expiry_type == 'WEEKLY':
            # Weekly pattern: UNDERLYING25DDSTRIKECE/PE
            return bool(re.search(r'\d{4}', symbol)) and not bool(re.search(r'\d{2}[A-Z]{3}', symbol))
        
        return True
    
    def _matches_expiry_months(self, option: SymbolMapping, expiry_months: List[str]) -> bool:
        """Check if option matches the specified expiry months."""
        if not option.expiry_date:
            return False
        
        option_month = option.expiry_date.month
        target_months = [MONTH_MAPPING.get(month.upper(), 0) for month in expiry_months]
        
        return option_month in target_months

    def _filter_symbols_with_existing_data(self, options: List[SymbolMapping]) -> List[SymbolMapping]:
        """Filter out symbols that already have recent data to avoid unnecessary API calls."""
        try:
            from src.database.models import OptionsOHLCV

            # Get symbols that have data from the last 2 days
            cutoff_date = self.current_date - timedelta(days=2)

            # Query for symbols with recent data
            existing_symbols_query = self.db.query(OptionsOHLCV.fyers_symbol).filter(
                OptionsOHLCV.datetime >= cutoff_date
            ).distinct()

            existing_symbols = {row.fyers_symbol for row in existing_symbols_query.all()}

            # Filter out symbols that already have recent data
            filtered_options = [
                opt for opt in options
                if opt.fyers_symbol not in existing_symbols
            ]

            skipped_count = len(options) - len(filtered_options)
            if skipped_count > 0:
                logger.info(f"   Skipped {skipped_count} symbols with recent data")

            return filtered_options

        except Exception as e:
            logger.warning(f"Error checking existing data, proceeding with all symbols: {e}")
            return options

    def _prioritize_symbols(self, options: List[SymbolMapping], strike_range: int) -> List[SymbolMapping]:
        """Prioritize symbols based on underlying type and strike price relevance with batch processing."""
        try:
            # Group symbols by underlying with caching
            underlying_groups = {}
            for option in options:
                underlying = self._extract_underlying_symbol_cached(option.nse_symbol)
                if underlying not in underlying_groups:
                    underlying_groups[underlying] = []
                underlying_groups[underlying].append(option)

            logger.info(f"📊 Grouped into {len(underlying_groups)} underlying symbols")

            # Prioritize underlyings
            prioritized_underlyings = self._prioritize_underlyings(list(underlying_groups.keys()))

            # Batch process spot prices for all underlyings
            self._batch_load_spot_prices(prioritized_underlyings)

            # Build final prioritized list with batch processing
            prioritized_options = []
            batch_size = 1000  # Process in batches of 1000 symbols
            total_underlyings = len(prioritized_underlyings)

            for i, underlying in enumerate(prioritized_underlyings, 1):
                if underlying in underlying_groups:
                    # Get relevant strikes for this underlying (using cached spot prices)
                    relevant_options = self._filter_by_strike_relevance_cached(
                        underlying_groups[underlying], underlying, strike_range
                    )

                    # Sort by expiry date (nearest first) and then by strike price
                    relevant_options.sort(key=lambda x: (x.expiry_date, abs(float(x.strike_price or 0))))

                    # Add to prioritized list in batches
                    prioritized_options.extend(relevant_options)

                    # Log progress for large datasets - show every 100 underlyings or at milestones
                    if i % 100 == 0 or i == total_underlyings or total_underlyings <= 50:
                        progress_pct = (i / total_underlyings) * 100
                        logger.info(f"📊 OPTIONS Prioritization: {i}/{total_underlyings} underlyings processed ({progress_pct:.1f}%) - {len(prioritized_options)} symbols selected")

            return prioritized_options

        except Exception as e:
            logger.error(f"Error prioritizing symbols: {e}")
            return options
    
    def _prioritize_underlyings(self, underlyings: List[str]) -> List[str]:
        """Prioritize underlying symbols: INDEX > Nifty 50 > Others."""
        index_symbols = []
        nifty50_symbols = []
        other_symbols = []
        
        for underlying in underlyings:
            if underlying in MAJOR_INDEX_SYMBOLS:
                index_symbols.append(underlying)
            elif underlying in NIFTY_50_SYMBOLS:
                nifty50_symbols.append(underlying)
            else:
                other_symbols.append(underlying)
        
        # Sort each category
        index_symbols.sort()
        nifty50_symbols.sort()
        other_symbols.sort()
        
        logger.info(f"📊 Prioritization breakdown:")
        logger.info(f"   INDEX symbols: {len(index_symbols)}")
        logger.info(f"   Nifty 50 symbols: {len(nifty50_symbols)}")
        logger.info(f"   Other symbols: {len(other_symbols)}")
        
        return index_symbols + nifty50_symbols + other_symbols
    
    def _filter_by_strike_relevance(self, options: List[SymbolMapping],
                                   underlying: str, strike_range: int) -> List[SymbolMapping]:
        """Enhanced strike filtering focusing on ATM and near-ATM options for better liquidity."""
        try:
            # Get current spot price
            spot_price = self.spot_price_service.get_spot_price(underlying)

            if spot_price is None:
                logger.warning(f"⚠️ No spot price for {underlying}, including all strikes")
                return options

            # ENHANCED STRIKE FILTERING - Focus on liquid strikes
            # 1. Prioritize ATM and near-ATM strikes (±10 strikes from ATM)
            atm_range = min(10, strike_range // 2)  # Tighter range for better liquidity

            # 2. Calculate ATM strike (nearest to spot price)
            strikes_with_distance = []
            for option in options:
                if option.strike_price:
                    strike = float(option.strike_price)
                    distance_from_spot = abs(strike - spot_price)
                    strikes_with_distance.append((option, strike, distance_from_spot))

            # Sort by distance from spot price
            strikes_with_distance.sort(key=lambda x: x[2])

            # 3. Select ATM and near-ATM options (prioritize liquid strikes)
            relevant_options = []
            ce_count = pe_count = 0
            max_options_per_type = atm_range  # Limit options per type (CE/PE)

            for option, strike, distance in strikes_with_distance:
                option_type = option.option_type

                # Limit number of CE and PE options to focus on most liquid
                if option_type == 'CE' and ce_count < max_options_per_type:
                    relevant_options.append(option)
                    ce_count += 1
                elif option_type == 'PE' and pe_count < max_options_per_type:
                    relevant_options.append(option)
                    pe_count += 1

                # Stop when we have enough options of both types
                if ce_count >= max_options_per_type and pe_count >= max_options_per_type:
                    break

            logger.debug(f"   {underlying}: {len(relevant_options)}/{len(options)} liquid strikes "
                        f"(CE: {ce_count}, PE: {pe_count}) around spot: ₹{spot_price:.0f}")

            return relevant_options

        except Exception as e:
            logger.error(f"Error filtering strikes for {underlying}: {e}")
            return options
    
    def _extract_underlying_symbol(self, nse_symbol: str) -> str:
        """Extract underlying symbol from OPTIONS NSE symbol."""
        from src.core.symbol_utils import SymbolUtils
        from src.database.models import MarketType
        return SymbolUtils.extract_underlying_symbol(nse_symbol, MarketType.OPTIONS)

    def _extract_underlying_symbol_cached(self, nse_symbol: str) -> str:
        """Extract underlying symbol with caching for performance."""
        if nse_symbol not in self._underlying_cache:
            self._underlying_cache[nse_symbol] = self._extract_underlying_symbol(nse_symbol)
        return self._underlying_cache[nse_symbol]

    def _batch_load_spot_prices(self, underlyings: List[str]) -> None:
        """Batch load spot prices for multiple underlyings to improve performance."""
        try:
            logger.debug(f"Batch loading spot prices for {len(underlyings)} underlyings")

            # Load spot prices in batches to avoid overwhelming the system
            batch_size = 10
            for i in range(0, len(underlyings), batch_size):
                batch = underlyings[i:i + batch_size]

                for underlying in batch:
                    if underlying not in self._spot_price_cache:
                        # Determine market type for better spot price lookup
                        market_type = 'INDEX' if underlying in MAJOR_INDEX_SYMBOLS else 'EQUITY'
                        spot_price = self.spot_price_service.get_spot_price(underlying, market_type)
                        self._spot_price_cache[underlying] = spot_price

                # Small delay between batches to avoid overwhelming the system
                if i + batch_size < len(underlyings):
                    import time
                    time.sleep(0.1)  # 100ms delay between batches

            logger.debug(f"Completed batch loading spot prices")

        except Exception as e:
            logger.error(f"Error in batch loading spot prices: {e}")

    def _filter_by_strike_relevance_cached(self, options: List[SymbolMapping],
                                         underlying: str, strike_range: int) -> List[SymbolMapping]:
        """Filter options by strike price relevance using cached spot prices."""
        try:
            # Use cached spot price
            spot_price = self._spot_price_cache.get(underlying)

            if spot_price is None:
                logger.warning(f"⚠️ No cached spot price for {underlying}, including all strikes")
                return options

            # Check cache for strike relevance calculation
            cache_key = f"{underlying}_{strike_range}_{spot_price}"
            if cache_key in self._strike_relevance_cache:
                lower_strike, upper_strike = self._strike_relevance_cache[cache_key]
            else:
                # Calculate and cache strike range
                lower_strike, upper_strike = self.spot_price_service.calculate_strike_levels(
                    spot_price, strike_range
                )
                self._strike_relevance_cache[cache_key] = (lower_strike, upper_strike)

            # Filter options by strike range
            relevant_options = []
            for option in options:
                if option.strike_price:
                    strike = float(option.strike_price)
                    if lower_strike <= strike <= upper_strike:
                        relevant_options.append(option)

            logger.debug(f"   {underlying}: {len(relevant_options)}/{len(options)} strikes in range "
                        f"₹{lower_strike:.0f}-₹{upper_strike:.0f} (spot: ₹{spot_price:.0f})")

            return relevant_options

        except Exception as e:
            logger.error(f"Error filtering strikes for {underlying}: {e}")
            return options

    def get_prioritization_summary(self, symbols: List[str]) -> Dict[str, int]:
        """Get summary of prioritized symbols by category."""
        try:
            summary = {
                'total': len(symbols),
                'index_symbols': 0,
                'nifty50_symbols': 0,
                'other_symbols': 0,
                'weekly_options': 0,
                'monthly_options': 0
            }
            
            for symbol in symbols:
                # Extract underlying
                underlying = self._extract_underlying_symbol(symbol)
                
                # Categorize by underlying type
                if underlying in MAJOR_INDEX_SYMBOLS:
                    summary['index_symbols'] += 1
                elif underlying in NIFTY_50_SYMBOLS:
                    summary['nifty50_symbols'] += 1
                else:
                    summary['other_symbols'] += 1
                
                # Categorize by expiry type
                if re.search(r'\d{2}[A-Z]{3}', symbol):
                    summary['monthly_options'] += 1
                elif re.search(r'\d{4}', symbol):
                    summary['weekly_options'] += 1
            
            return summary
            
        except Exception as e:
            logger.error(f"Error generating prioritization summary: {e}")
            return {'total': len(symbols)}

    def clear_caches(self) -> None:
        """Clear all performance caches to free memory."""
        self._underlying_cache.clear()
        self._spot_price_cache.clear()
        self._strike_relevance_cache.clear()
        logger.debug("Cleared all performance caches")

    def get_cache_stats(self) -> Dict[str, int]:
        """Get statistics about cache usage."""
        return {
            'underlying_cache_size': len(self._underlying_cache),
            'spot_price_cache_size': len(self._spot_price_cache),
            'strike_relevance_cache_size': len(self._strike_relevance_cache)
        }

    def __del__(self):
        """Cleanup database connection."""
        if hasattr(self, 'db') and self.db:
            self.db.close()
