"""
Centralized Rate Limiting Service
Respects config.yaml rate limiting settings and provides consistent rate limiting across the project.
"""

import time
import asyncio
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from threading import Lock

from src.core.logging import get_logger
from src.core.config import settings

logger = get_logger(__name__)


class RateLimiter:
    """
    Centralized rate limiter that respects config.yaml settings.
    Thread-safe and supports both sync and async operations.
    """
    
    def __init__(self):
        """Initialize rate limiter with config.yaml settings."""
        self._lock = Lock()
        self._last_request_time = 0.0
        self._request_count = 0

        # Load settings from config.yaml
        self.min_delay_seconds = settings.rate_limit.min_delay_seconds
        self.max_retries = settings.rate_limit.max_retries
        self.retry_backoff = settings.rate_limit.retry_backoff

        # DYNAMIC RATE LIMITING - Adaptive performance optimization
        self._success_count = 0
        self._failure_count = 0
        self._adaptive_delay = self.min_delay_seconds
        self._last_adjustment_time = time.time()
        self._adjustment_interval = 60  # Adjust every 60 seconds
        
        logger.info(f"🚦 Rate limiter initialized with config.yaml settings:")
        logger.info(f"   Min delay: {self.min_delay_seconds}s")
        logger.info(f"   Max retries: {self.max_retries}")
        logger.info(f"   Retry backoff: {self.retry_backoff}s")
    
    def apply_rate_limit(self) -> None:
        """Apply DYNAMIC rate limiting between API requests (synchronous)."""
        with self._lock:
            current_time = time.time()
            time_since_last = current_time - self._last_request_time

            # Use adaptive delay instead of fixed delay
            effective_delay = self._get_adaptive_delay()

            if time_since_last < effective_delay:
                sleep_time = effective_delay - time_since_last
                logger.debug(f"⏱️  Dynamic rate limiting: sleeping for {sleep_time:.3f}s (adaptive: {effective_delay:.3f}s)")
                time.sleep(sleep_time)

            self._last_request_time = time.time()
            self._request_count += 1
    
    async def apply_rate_limit_async(self) -> None:
        """Apply DYNAMIC rate limiting between API requests (asynchronous)."""
        with self._lock:
            current_time = time.time()
            time_since_last = current_time - self._last_request_time

            # Use adaptive delay instead of fixed delay
            effective_delay = self._get_adaptive_delay()

            if time_since_last < effective_delay:
                sleep_time = effective_delay - time_since_last
                logger.debug(f"⏱️  Dynamic rate limiting (async): sleeping for {sleep_time:.3f}s")
                # Release lock before sleeping
                self._lock.release()
                await asyncio.sleep(sleep_time)
                self._lock.acquire()

            self._last_request_time = time.time()
            self._request_count += 1
    
    def get_retry_delay(self, attempt: int) -> float:
        """
        Calculate retry delay using exponential backoff from config.yaml.
        
        Args:
            attempt: Current retry attempt (0-based)
            
        Returns:
            Delay in seconds
        """
        if attempt >= self.max_retries:
            return 0.0
        
        # Exponential backoff: base_delay * (2 ^ attempt)
        delay = self.retry_backoff * (2 ** attempt)
        logger.debug(f"🔄 Retry delay for attempt {attempt + 1}: {delay:.1f}s")
        return delay
    
    def should_retry(self, attempt: int) -> bool:
        """
        Check if we should retry based on config.yaml max_retries.
        
        Args:
            attempt: Current retry attempt (0-based)
            
        Returns:
            True if should retry, False otherwise
        """
        return attempt < self.max_retries
    
    def sleep_with_config(self, duration: Optional[float] = None) -> None:
        """
        Sleep for specified duration or min_delay_seconds from config.
        
        Args:
            duration: Optional duration to sleep. If None, uses min_delay_seconds
        """
        sleep_time = duration if duration is not None else self.min_delay_seconds
        logger.debug(f"😴 Sleeping for {sleep_time:.3f}s (config-based)")
        time.sleep(sleep_time)
    
    async def sleep_with_config_async(self, duration: Optional[float] = None) -> None:
        """
        Async sleep for specified duration or min_delay_seconds from config.
        
        Args:
            duration: Optional duration to sleep. If None, uses min_delay_seconds
        """
        sleep_time = duration if duration is not None else self.min_delay_seconds
        logger.debug(f"😴 Sleeping (async) for {sleep_time:.3f}s (config-based)")
        await asyncio.sleep(sleep_time)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics."""
        return {
            'total_requests': self._request_count,
            'last_request_time': datetime.fromtimestamp(self._last_request_time) if self._last_request_time > 0 else None,
            'min_delay_seconds': self.min_delay_seconds,
            'max_retries': self.max_retries,
            'retry_backoff': self.retry_backoff
        }
    
    def reset_stats(self) -> None:
        """Reset rate limiter statistics."""
        with self._lock:
            self._request_count = 0
            self._last_request_time = 0.0
            self._success_count = 0
            self._failure_count = 0
            self._adaptive_delay = self.min_delay_seconds
        logger.info("📊 Rate limiter statistics reset")

    def _get_adaptive_delay(self) -> float:
        """Get adaptive delay based on recent success/failure rates."""
        current_time = time.time()

        # Adjust delay every adjustment_interval seconds
        if current_time - self._last_adjustment_time >= self._adjustment_interval:
            self._adjust_adaptive_delay()
            self._last_adjustment_time = current_time

        return self._adaptive_delay

    def _adjust_adaptive_delay(self) -> None:
        """Adjust adaptive delay based on success/failure ratio."""
        total_requests = self._success_count + self._failure_count

        if total_requests < 10:  # Need minimum sample size
            return

        success_rate = self._success_count / total_requests

        # INTELLIGENT RATE ADJUSTMENT
        if success_rate >= 0.9:  # High success rate - can be more aggressive
            self._adaptive_delay = max(0.05, self._adaptive_delay * 0.9)  # Reduce delay by 10%
            logger.debug(f"🚀 High success rate ({success_rate:.1%}) - reducing delay to {self._adaptive_delay:.3f}s")
        elif success_rate <= 0.5:  # Low success rate - be more conservative
            self._adaptive_delay = min(0.2, self._adaptive_delay * 1.2)  # Increase delay by 20%
            logger.debug(f"⚠️ Low success rate ({success_rate:.1%}) - increasing delay to {self._adaptive_delay:.3f}s")

        # Reset counters for next adjustment period
        self._success_count = 0
        self._failure_count = 0

    def record_success(self) -> None:
        """Record a successful API call for adaptive rate limiting."""
        with self._lock:
            self._success_count += 1

    def record_failure(self) -> None:
        """Record a failed API call for adaptive rate limiting."""
        with self._lock:
            self._failure_count += 1

    def should_skip_retry_for_no_data(self, error_message: str) -> bool:
        """INTELLIGENT RETRY LOGIC - Skip retries for permanent failures."""
        no_data_indicators = [
            'no_data', 'no data', 'candles": []', 'empty response',
            'symbol not found', 'invalid symbol'
        ]

        error_lower = str(error_message).lower()
        return any(indicator in error_lower for indicator in no_data_indicators)


# Global rate limiter instance
rate_limiter = RateLimiter()


def apply_rate_limit() -> None:
    """Convenience function for applying rate limit."""
    rate_limiter.apply_rate_limit()


async def apply_rate_limit_async() -> None:
    """Convenience function for applying async rate limit."""
    await rate_limiter.apply_rate_limit_async()


def get_retry_delay(attempt: int) -> float:
    """Convenience function for getting retry delay."""
    return rate_limiter.get_retry_delay(attempt)


def should_retry(attempt: int) -> bool:
    """Convenience function for checking if should retry."""
    return rate_limiter.should_retry(attempt)


def sleep_with_config(duration: Optional[float] = None) -> None:
    """Convenience function for config-based sleep."""
    rate_limiter.sleep_with_config(duration)


async def sleep_with_config_async(duration: Optional[float] = None) -> None:
    """Convenience function for config-based async sleep."""
    await rate_limiter.sleep_with_config_async(duration)
